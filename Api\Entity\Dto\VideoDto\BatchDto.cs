using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 批次查询DTO
    /// </summary>
    public class BatchQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 批次名称(模糊查询)
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatorId { get; set; }

        /// <summary>
        /// 状态:0下线,1上线
        /// </summary>
        public byte? Status { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 批次创建DTO
    /// </summary>
    public class BatchCreateDto
    {
        /// <summary>
        /// 批次名称
        /// </summary>
        [Required(ErrorMessage = "批次名称不能为空")]
        [MaxLength(100, ErrorMessage = "批次名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 批次描述
        /// </summary>
        [MaxLength(255, ErrorMessage = "批次描述长度不能超过255个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 关联视频ID
        /// </summary>
        [Required(ErrorMessage = "关联视频ID不能为空")]
        public int VideoId { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [Required(ErrorMessage = "开始时间不能为空")]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Required(ErrorMessage = "结束时间不能为空")]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RedPacketAmount { get; set; }
    }

    /// <summary>
    /// 批次响应DTO
    /// </summary>
    public class BatchResponseDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 批次名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 批次描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 关联视频ID
        /// </summary>
        public int VideoId { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string VideoTitle { get; set; } = string.Empty;

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? VideoDescription { get; set; }

        /// <summary>
        /// 封面URL
        /// </summary>
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 视频时长
        /// </summary>
        public int VideoDuration { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 问题列表
        /// </summary>
        public List<VideoQuestionDto>? Questions { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int CreatorId { get; set; }

        /// <summary>
        /// 状态:0下线,1上线
        /// </summary>
        public byte Status { get; set; }

        /// <summary>
        /// 当前参与人数
        /// </summary>
        public int CurrentParticipants { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RedPacketAmount { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatorName { get; set; }

        /// <summary>
        /// 统计信息
        /// </summary>
        public BatchStatisticsDto? Statistics { get; set; }
    }

    /// <summary>
    /// 批次统计DTO
    /// </summary>
    public class BatchStatisticsDto
    {
        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 完播次数
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 完播率
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 新增用户数
        /// </summary>
        public int NewUserCount { get; set; }

        /// <summary>
        /// 答题正确次数
        /// </summary>
        public int CorrectAnswerCount { get; set; }

        /// <summary>
        /// 答题总次数
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 红包发放次数
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// 红包发放总金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 总批次数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 活跃批次数
        /// </summary>
        public int ActiveCount { get; set; }

        /// <summary>
        /// 已完成批次数
        /// </summary>
        public int CompletedCount { get; set; }

        /// <summary>
        /// 总参与人数
        /// </summary>
        public int TotalParticipants { get; set; }

        /// <summary>
        /// 红包总金额
        /// </summary>
        public decimal TotalRedPacketAmount { get; set; }
    }

    /// <summary>
    /// 批次状态更新DTO
    /// </summary>
    public class BatchStatusUpdateDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 状态:0下线,1上线
        /// </summary>
        [Range(0, 1, ErrorMessage = "状态值必须为0或1")]
        public byte Status { get; set; }
    }
}
