using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 系统配置数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SystemConfigDAL(MyContext context) : BaseQueryDLL<SystemConfig, SystemConfigDAL.Queryable>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 系统配置查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 配置键(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? ConfigKey { get; set; }

            /// <summary>
            /// 配置分组(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? ConfigGroup { get; set; }

            /// <summary>
            /// 配置类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? ConfigType { get; set; }

            /// <summary>
            /// 是否启用
            /// </summary>
            [Query(QueryOperator.等于)]
            public bool? IsEnabled { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.升序, orderPriority: 1)]
            public string? ConfigGroup_Sort { get; set; }

            /// <summary>
            /// 排序字段2
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.升序, orderPriority: 2)]
            public string? ConfigKey_Sort { get; set; }
        }

        /// <summary>
        /// 根据配置键获取配置值
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>配置值</returns>
        public async Task<string?> GetConfigValueAsync(string configKey)
        {
            var config = await _context.Set<SystemConfig>()
                .FirstOrDefaultAsync(sc => sc.ConfigKey == configKey);
            return config?.ConfigValue;
        }

        /// <summary>
        /// 根据配置键获取配置
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>配置实体</returns>
        public async Task<SystemConfig?> GetByKeyAsync(string configKey)
        {
            return await _context.Set<SystemConfig>()
                .FirstOrDefaultAsync(sc => sc.ConfigKey == configKey);
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <param name="excludeId">排除的配置ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsKeyAsync(string configKey, int? excludeId = null)
        {
            var query = _context.Set<SystemConfig>().Where(sc => sc.ConfigKey == configKey);
            if (excludeId.HasValue)
            {
                query = query.Where(sc => sc.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        /// <summary>
        /// 根据分组获取配置列表
        /// </summary>
        /// <param name="configGroup">配置分组</param>
        /// <returns>配置列表</returns>
        public async Task<List<SystemConfig>> GetByGroupAsync(string configGroup)
        {
            return await _context.Set<SystemConfig>()
                .Where(sc => sc.GroupName == configGroup)
                .OrderBy(sc => sc.ConfigKey)
                .ToListAsync();
        }

        /// <summary>
        /// 获取所有配置分组
        /// </summary>
        /// <returns>分组列表</returns>
        public async Task<List<string>> GetAllGroupsAsync()
        {
            return await _context.Set<SystemConfig>()
                .Where(sc => sc.GroupName != null)
                .Select(sc => sc.GroupName!)
                .Distinct()
                .OrderBy(g => g)
                .ToListAsync();
        }

        /// <summary>
        /// 更新配置值
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <param name="configValue">配置值</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateConfigValueAsync(string configKey, string configValue)
        {
            var config = await GetByKeyAsync(configKey);
            if (config == null) return false;

            config.ConfigValue = configValue;
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="configs">配置字典</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchUpdateAsync(Dictionary<string, string> configs)
        {
            if (configs == null || configs.Count == 0) return false;

            var configKeys = configs.Keys.ToList();
            var existingConfigs = await _context.Set<SystemConfig>()
                .Where(sc => configKeys.Contains(sc.ConfigKey))
                .ToListAsync();

            foreach (var config in existingConfigs)
            {
                if (configs.ContainsKey(config.ConfigKey))
                {
                    config.ConfigValue = configs[config.ConfigKey];
                }
            }

            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取微信相关配置
        /// </summary>
        /// <returns>微信配置字典</returns>
        public async Task<Dictionary<string, string>> GetWechatConfigAsync()
        {
            var configs = await GetByGroupAsync("微信配置");
            return configs.ToDictionary(c => c.ConfigKey, c => c.ConfigValue ?? string.Empty);
        }

        /// <summary>
        /// 获取红包相关配置
        /// </summary>
        /// <returns>红包配置字典</returns>
        public async Task<Dictionary<string, string>> GetRewardConfigAsync()
        {
            var configs = await GetByGroupAsync("红包配置");
            return configs.ToDictionary(c => c.ConfigKey, c => c.ConfigValue ?? string.Empty);
        }

        /// <summary>
        /// 获取系统相关配置
        /// </summary>
        /// <returns>系统配置字典</returns>
        public async Task<Dictionary<string, string>> GetSystemConfigAsync()
        {
            var configs = await GetByGroupAsync("系统配置");
            return configs.ToDictionary(c => c.ConfigKey, c => c.ConfigValue ?? string.Empty);
        }

        /// <summary>
        /// 根据ID获取系统配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>配置实体</returns>
        public async Task<SystemConfig?> GetByIdAsync(int id)
        {
            return await _context.Set<SystemConfig>().FindAsync(id);
        }

        /// <summary>
        /// 获取分页系统配置列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<SystemConfig>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderBy(x => x.GroupName).ThenBy(x => x.ConfigKey));
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsConfigKeyAsync(string configKey, int? excludeId = null)
        {
            var query = _context.Set<SystemConfig>().Where(sc => sc.ConfigKey == configKey);
            if (excludeId.HasValue)
            {
                query = query.Where(sc => sc.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        /// <summary>
        /// 根据配置键获取配置
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>系统配置</returns>
        public async Task<SystemConfig?> GetByConfigKeyAsync(string configKey)
        {
            return await _context.Set<SystemConfig>()
                .FirstOrDefaultAsync(sc => sc.ConfigKey == configKey);
        }

        /// <summary>
        /// 更新配置状态
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateConfigStatusAsync(int id, bool isEnabled)
        {
            var config = await GetByIdAsync(id);
            if (config == null) return false;

            config.IsEnabled = (byte)(isEnabled ? 1 : 0);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取所有启用的配置
        /// </summary>
        /// <returns>启用的配置列表</returns>
        public async Task<List<SystemConfig>> GetAllEnabledConfigsAsync()
        {
            return await _context.Set<SystemConfig>()
                .Where(sc => sc.IsEnabled == 1)
                .OrderBy(sc => sc.ConfigKey)
                .ToListAsync();
        }

        /// <summary>
        /// 根据配置类型获取配置
        /// </summary>
        /// <param name="configType">配置类型</param>
        /// <returns>配置列表</returns>
        public async Task<List<SystemConfig>> GetByConfigTypeAsync(string configType)
        {
            return await _context.Set<SystemConfig>()
                .Where(sc => sc.ConfigType == configType)
                .OrderBy(sc => sc.ConfigKey)
                .ToListAsync();
        }

        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="configs">配置列表</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchUpdateConfigsAsync(List<SystemConfig> configs)
        {
            if (configs == null || configs.Count == 0) return false;

            _context.Set<SystemConfig>().UpdateRange(configs);
            return await _context.SaveChangesAsync() > 0;
        }
    }
}
