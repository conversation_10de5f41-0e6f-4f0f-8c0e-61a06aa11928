using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.SysDAL
{
    /// <summary>
    /// 日志数据访问层实现类
    /// 负责日志相关的数据库操作
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="context">数据库上下文</param>
    [Dependency(DependencyType.Scoped)]
    public class SysLogDAL(MyContext context) : BaseQueryDLL<SysLog, SysLogDAL.Queryable>(context)
    {
        /// <summary>
        /// 数据库上下文实例
        /// </summary>
        private readonly MyContext _context = context;

        /// <summary>
        /// 日志查询条件模型类
        /// 定义了日志查询时可用的过滤条件
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 日志ID
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? Id { get; set; }

            /// <summary>
            /// 用户ID
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? UserId { get; set; }

            /// <summary>
            /// 用户名
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Username { get; set; }

            /// <summary>
            /// 操作描述
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Operation { get; set; }

            /// <summary>
            /// 操作方法
            /// 使用模糊匹配查询
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Method { get; set; }

            /// <summary>
            /// 日志类型
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? LogType { get; set; }

            /// <summary>
            /// 日志级别
            /// 使用精确匹配查询
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? LogLevel { get; set; }

            /// <summary>
            /// 创建时间范围开始
            /// </summary>
            [Query(QueryOperator.日期范围, columnName: "CreateTime", relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 创建时间范围结束
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 创建时间排序标志
            /// 用于按创建时间降序排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "CreateTime", orderDirection: OrderDirection.降序, orderPriority: 1)]
            public int? OrderByCreateTime { get; set; }
        }



        /// <summary>
        /// 分页获取日志列表
        /// </summary>
        /// <param name="queryable">查询条件，包含分页参数和筛选条件</param>
        /// <returns>分页结果，包含日志列表和总记录数</returns>
        public Task<PageEntity<SysLog>> GetPageAsync(Queryable queryable)
        => GetPageDataAsync(queryable);


        /// <summary>
        /// 清理指定日期之前的日志
        /// </summary>
        /// <param name="beforeDate">截止日期</param>
        /// <param name="logType">日志类型</param>
        /// <returns>操作结果</returns>
        public async Task<bool> ClearLogsAsync(DateTime beforeDate, string? logType = null)
        {
            var query = _context.SysLogs.Where(x => x.CreateTime < beforeDate);

            if (!string.IsNullOrWhiteSpace(logType))
                query = query.Where(x => x.LogType == logType);


            return await query.ExecuteDeleteAsync() > 0;
        }

        /// <summary>
        /// 获取分页数据列表
        /// </summary>
        /// <param name="queryable">查询条件模型</param>
        /// <returns>分页结果</returns>
        public Task<PageEntity<SysLog>> GetPageDataAsync(Queryable queryable)
        => GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));




    }
}