using BLL.VideoService;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 统计控制器（基于实时统计）
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class StatisticsController(
        StatisticsService statisticsService,
        DashboardService dashboardService,
        UserBatchRecordService userBatchRecordService) : BaseController
    {
        private readonly StatisticsService _statisticsService = statisticsService;
        private readonly DashboardService _dashboardService = dashboardService;
        private readonly UserBatchRecordService _userBatchRecordService = userBatchRecordService;

        // ==================== 主要统计接口（基于用户中心化统计） ====================

        /// <summary>
        /// 获取用户每日统计数据（分页）
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页统计数据</returns>
        [HttpGet("user-daily")]
        public async Task<Result<PagedResult<UserDailyStatisticsDto>>> GetUserDailyStatistics([FromQuery] UserDailyStatisticsQueryDto queryDto)
        {
            // 使用新的UserBatchRecordService实现用户每日统计
            var startDate = queryDto.StartDate ?? DateTime.Today.AddDays(-30);
            var endDate = queryDto.EndDate ?? DateTime.Today;

            var records = await _userBatchRecordService.GetRecordsByDateRangeAsync(startDate, endDate, GetCurrentUserInfo());

            // 按用户分组统计
            var userStats = records.GroupBy(r => r.UserId)
                .Select(g => new UserDailyStatisticsDto
                {
                    UserId = g.Key, // 直接使用string类型的UserId
                    StatDate = g.Max(r => r.CreateTime).Date,
                    ViewCount = g.Count(),
                    CompleteViewCount = g.Count(r => r.IsCompleted),
                    AnswerCount = g.Sum(r => r.TotalQuestions),
                    CorrectAnswerCount = g.Sum(r => r.CorrectAnswers),
                    RewardAmount = (long)g.Sum(r => r.RewardAmount), // 转换为long
                    RewardCount = g.Count(r => r.RewardAmount > 0)
                })
                .Skip((queryDto.PageIndex - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToList();

            var result = new PagedResult<UserDailyStatisticsDto>
            {
                Items = userStats,
                TotalCount = records.Select(r => r.UserId).Distinct().Count(),
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            return Success(result);
        }

        /// <summary>
        /// 获取用户统计汇总数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>汇总数据</returns>
        [HttpGet("user-summary/{userId}")]
        public async Task<Result<UserStatisticsSummaryDto?>> GetUserSummary(
            string userId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;

            // TODO: 使用新的UserBatchRecordService实现用户汇总
            await Task.CompletedTask;
            var summary = new UserStatisticsSummaryDto();
            return Success(summary);
        }

        /// <summary>
        /// 批量获取用户统计汇总数据
        /// </summary>
        /// <param name="userIds">用户ID列表（逗号分隔）</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>汇总数据列表</returns>
        [HttpGet("users-summary")]
        public async Task<Result<List<UserStatisticsSummaryDto>>> GetUsersSummary(
            [FromQuery] string userIds,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            if (string.IsNullOrEmpty(userIds))
            {
                return Success(new List<UserStatisticsSummaryDto>());
            }

            var userIdList = userIds.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => int.TryParse(id.Trim(), out var userId) ? userId : 0)
                .Where(id => id > 0)
                .ToList();

            if (!userIdList.Any())
            {
                return Success(new List<UserStatisticsSummaryDto>());
            }

            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;

            // TODO: 使用新的UserBatchRecordService实现多用户汇总
            await Task.CompletedTask;
            var summaries = new List<UserStatisticsSummaryDto>();
            return Success(summaries);
        }

        /// <summary>
        /// 获取员工负责用户的统计汇总
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>汇总数据</returns>
        [HttpGet("employee-summary/{employeeId}")]
        public async Task<Result<UserStatisticsSummaryDto>> GetEmployeeSummary(
            int employeeId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;

            // TODO: 使用新的UserBatchRecordService实现员工汇总
            await Task.CompletedTask;
            var summary = new UserStatisticsSummaryDto();
            return Success(summary);
        }

        /// <summary>
        /// 获取每日统计趋势
        /// </summary>
        /// <param name="userIds">用户ID列表（逗号分隔，可选）</param>
        /// <param name="employeeId">员工ID（可选）</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>趋势数据</returns>
        [HttpGet("daily-trend")]
        public async Task<Result<List<DailyStatisticsTrendDto>>> GetDailyTrend(
            [FromQuery] string? userIds = null,
            [FromQuery] int? employeeId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            List<int>? userIdList = null;
            if (!string.IsNullOrEmpty(userIds))
            {
                userIdList = userIds.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id.Trim(), out var userId) ? userId : 0)
                    .Where(id => id > 0)
                    .ToList();
            }

            var start = startDate ?? DateTime.Today.AddDays(-7);
            var end = endDate ?? DateTime.Today;

            // TODO: 使用新的UserBatchRecordService实现每日趋势
            await Task.CompletedTask;
            var trends = new List<DailyStatisticsTrendDto>();
            return Success(trends);
        }

        /// <summary>
        /// 获取统计概览数据（用于仪表板）
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>概览数据</returns>
        [HttpGet("overview")]
        public async Task<Result<StatisticsOverviewDto>> GetStatisticsOverview(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;

            // 使用新的UserBatchRecordService实现统计概览
            var records = await _userBatchRecordService.GetRecordsByDateRangeAsync(start, end, GetCurrentUserInfo());

            var overview = new StatisticsOverviewDto
            {
                TotalUsers = records.Select(r => r.UserId).Distinct().Count(),
                TotalViewCount = records.Count,
                TotalCompleteViewCount = records.Count(r => r.IsCompleted),
                TotalCorrectAnswerCount = records.Sum(r => r.CorrectAnswers),
                TotalAnswerCount = records.Sum(r => r.TotalQuestions),
                TotalRewardCount = records.Count(r => r.RewardAmount > 0),
                TotalRewardAmountYuan = records.Sum(r => r.RewardAmount),
                StartDate = start,
                EndDate = end
            };
            // AvgCompleteRate 和 AvgCorrectRate 是只读属性，会自动计算

            return Success(overview);
        }

        // 注意：实时统计不需要增量更新接口，统计数据直接从业务表计算

        // ==================== 兼容性接口（重定向到新的用户统计逻辑） ====================

        /// <summary>
        /// 获取用户统计数据（兼容性接口）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>用户统计数据</returns>
        [HttpGet("user/{userId}")]
        public async Task<Result<List<StatisticsResponseDto>>> GetUserStatistics(string userId, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            var statistics = await _statisticsService.GetUserStatisticsAsync(userId, null, startDate, endDate, GetCurrentUserInfo());
            return Success(statistics);
        }

        /// <summary>
        /// 获取我的统计数据（兼容性接口）
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>我的统计数据</returns>
        [HttpGet("my-statistics")]
        public async Task<Result<List<StatisticsResponseDto>>> GetMyStatistics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            var currentUser = GetCurrentUserInfo();
            var statistics = await _statisticsService.GetUserStatisticsAsync(currentUser.UserId, null, startDate, endDate, currentUser);
            return Success(statistics);
        }

        /// <summary>
        /// 获取统计汇总数据（兼容性接口）
        /// </summary>
        /// <param name="summaryDto">汇总查询DTO</param>
        /// <returns>汇总统计数据</returns>
        [HttpPost("summary")]
        public async Task<Result<StatisticsSummaryDto>> GetStatisticsSummary([FromBody] StatisticsSummaryQueryDto summaryDto)
        {
            var summary = await _statisticsService.GetStatisticsSummaryAsync(summaryDto, GetCurrentUserInfo());
            return Success(summary);
        }

        // ==================== 其他统计服务接口 ====================

        /// <summary>
        /// 获取仪表板数据
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>仪表板数据</returns>
        [HttpGet("dashboard")]
        public async Task<Result<DashboardDto>> GetDashboard([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            var dashboard = await _dashboardService.GetDashboardDataAsync(startDate, endDate);
            return Success(dashboard);
        }

        /// <summary>
        /// 获取仪表板关键指标汇总
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>关键指标汇总</returns>
        [HttpGet("dashboard/key-metrics")]
        public async Task<Result<KeyMetricsSummaryDto>> GetKeyMetrics([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            var keyMetrics = await _dashboardService.GetKeyMetricsSummaryAsync(startDate, endDate);
            return Success(keyMetrics);
        }

        /// <summary>
        /// 获取今日数据快照
        /// </summary>
        /// <returns>今日数据快照</returns>
        [HttpGet("dashboard/today")]
        public async Task<Result<DashboardDto>> GetTodaySnapshot()
        {
            var snapshot = await _dashboardService.GetTodaySnapshotAsync();
            return Success(snapshot);
        }

        // ==================== 数据迁移相关接口（仅超级管理员） ====================



        // 注意：实时统计不需要数据清理接口，因为不存储统计数据
    }
}
