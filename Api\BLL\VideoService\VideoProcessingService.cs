using Common.Autofac;
using Common.Exceptions;
using Entity.Dto.VideoDto;
using Entity.Dto;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using FFMpegCore;
using FFMpegCore.Enums;
using System.Collections.Concurrent;

namespace BLL.VideoService
{
    /// <summary>
    /// 视频处理服务类
    /// </summary>
    [Dependency(DependencyType.Singleton)]
    public class VideoProcessingService(ILogger<VideoProcessingService> logger, IServiceProvider serviceProvider)
    {
        private readonly ILogger<VideoProcessingService> _logger = logger;
        private readonly IServiceProvider _serviceProvider = serviceProvider;
        private readonly string _uploadBasePath = Path.Combine(Environment.CurrentDirectory, "wwwroot");
        private readonly string[] _allowedVideoExtensions = { ".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm" };
        private const long MaxFileSize = 2L * 1024 * 1024 * 1024; // 2GB

        // 压缩进度跟踪
        private static readonly ConcurrentDictionary<string, VideoCompressionProgress> _compressionProgress = new();

        /// <summary>
        /// 视频压缩进度信息
        /// </summary>
        public class VideoCompressionProgress
        {
            public string FileId { get; set; } = string.Empty;
            public int Progress { get; set; } = 0;
            public string Status { get; set; } = "waiting"; // waiting, processing, completed, failed
            public string? ErrorMessage { get; set; }
            public long OriginalSize { get; set; }
            public long CompressedSize { get; set; }
            public string? CompressedUrl { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime? EndTime { get; set; }
        }

        /// <summary>
        /// 处理视频上传
        /// </summary>
        /// <param name="uploadDto">上传DTO</param>
        /// <returns>上传响应DTO</returns>
        public async Task<VideoUploadResponseDto> ProcessVideoUploadAsync(VideoUploadDto uploadDto)
        {
            try
            {
                // 验证文件
                ValidateVideoFile(uploadDto.VideoFile);

                // 生成文件ID和路径
                var fileId = Guid.NewGuid().ToString("N");
                var dateFolder = DateTime.Now.ToString("yyyy-MM-dd");
                var originalFolder = Path.Combine(_uploadBasePath, "videos", "original", dateFolder);

                // 确保目录存在
                Directory.CreateDirectory(originalFolder);

                // 获取文件扩展名
                var extension = Path.GetExtension(uploadDto.VideoFile.FileName).ToLowerInvariant();
                var originalFileName = $"{fileId}_original{extension}";
                var originalFilePath = Path.Combine(originalFolder, originalFileName);

                // 保存原始文件
                using (var stream = new FileStream(originalFilePath, FileMode.Create))
                {
                    await uploadDto.VideoFile.CopyToAsync(stream);
                }

                _logger.LogInformation($"原始视频文件已保存: {originalFilePath}");

                // 获取视频信息
                var videoInfo = await FFProbe.AnalyseAsync(originalFilePath);
                var duration = (int)videoInfo.Duration.TotalSeconds;
                var resolution = $"{videoInfo.PrimaryVideoStream?.Width}x{videoInfo.PrimaryVideoStream?.Height}";

                // 初始化压缩进度
                var compressionProgress = new VideoCompressionProgress
                {
                    FileId = fileId,
                    Progress = 0,
                    Status = "waiting",
                    OriginalSize = uploadDto.VideoFile.Length,
                    StartTime = DateTime.Now
                };
                _compressionProgress[fileId] = compressionProgress;

                // 构建响应
                var response = new VideoUploadResponseDto
                {
                    OriginalVideoUrl = $"/videos/original/{dateFolder}/{originalFileName}",
                    Duration = duration,
                    OriginalFileSize = uploadDto.VideoFile.Length,
                    VideoFormat = extension.TrimStart('.'),
                    Resolution = resolution,
                    CompressionInProgress = uploadDto.EnableCompression,
                    CompressionProgress = 0,
                    FileId = fileId,
                    CompressedVideoUrl = uploadDto.EnableCompression ? null : $"/videos/original/{dateFolder}/{originalFileName}",
                    CompressedFileSize = uploadDto.EnableCompression ? 0 : uploadDto.VideoFile.Length
                };

                // 如果启用压缩，启动异步压缩任务
                if (uploadDto.EnableCompression)
                {
                    _ = Task.Run(async () => await CompressVideoAsync(originalFilePath, fileId, uploadDto.CompressionQuality, dateFolder));
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "视频上传处理失败");
                throw new BusinessException($"视频上传处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理封面上传
        /// </summary>
        /// <param name="coverFile">封面文件</param>
        /// <returns>封面URL</returns>
        public async Task<string> ProcessCoverUploadAsync(IFormFile coverFile)
        {
            try
            {
                // 验证封面文件
                ValidateCoverFile(coverFile);

                // 生成文件ID和路径
                var fileId = Guid.NewGuid().ToString("N");
                var dateFolder = DateTime.Now.ToString("yyyy-MM-dd");
                var coverFolder = Path.Combine(_uploadBasePath, "covers", dateFolder);

                // 确保目录存在
                Directory.CreateDirectory(coverFolder);

                // 获取文件扩展名
                var extension = Path.GetExtension(coverFile.FileName).ToLowerInvariant();
                var coverFileName = $"{fileId}_cover{extension}";
                var coverFilePath = Path.Combine(coverFolder, coverFileName);

                // 保存封面文件
                using (var stream = new FileStream(coverFilePath, FileMode.Create))
                {
                    await coverFile.CopyToAsync(stream);
                }

                _logger.LogInformation($"封面文件已保存: {coverFilePath}");

                return $"/covers/{dateFolder}/{coverFileName}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "封面上传处理失败");
                throw new BusinessException($"封面上传处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证视频文件
        /// </summary>
        /// <param name="file">文件</param>
        private void ValidateVideoFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new BusinessException("视频文件不能为空");

            if (file.Length > MaxFileSize)
                throw new BusinessException($"视频文件大小不能超过 {MaxFileSize / 1024 / 1024}MB");

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (!_allowedVideoExtensions.Contains(extension))
                throw new BusinessException($"不支持的视频格式，支持的格式: {string.Join(", ", _allowedVideoExtensions)}");
        }

        /// <summary>
        /// 验证封面文件
        /// </summary>
        /// <param name="file">文件</param>
        private void ValidateCoverFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new BusinessException("封面文件不能为空");

            // 封面文件大小限制为5MB
            const long maxCoverSize = 5 * 1024 * 1024;
            if (file.Length > maxCoverSize)
                throw new BusinessException($"封面文件大小不能超过 {maxCoverSize / 1024 / 1024}MB");

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var allowedImageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
            if (!allowedImageExtensions.Contains(extension))
                throw new BusinessException($"不支持的图片格式，支持的格式: {string.Join(", ", allowedImageExtensions)}");
        }

        /// <summary>
        /// 异步压缩视频
        /// </summary>
        /// <param name="originalFilePath">原始文件路径</param>
        /// <param name="fileId">文件ID</param>
        /// <param name="compressionQuality">压缩质量(1-10)</param>
        /// <param name="dateFolder">日期文件夹</param>
        private async Task CompressVideoAsync(string originalFilePath, string fileId, int compressionQuality, string dateFolder)
        {
            try
            {
                var progress = _compressionProgress[fileId];
                progress.Status = "processing";
                progress.Progress = 0;

                // 获取视频总时长用于进度计算
                var videoInfo = await FFProbe.AnalyseAsync(originalFilePath);
                var totalDuration = videoInfo.Duration;

                // 创建压缩文件夹
                var compressedFolder = Path.Combine(_uploadBasePath, "videos", "compressed", dateFolder);
                Directory.CreateDirectory(compressedFolder);

                // 生成压缩文件路径
                var extension = Path.GetExtension(originalFilePath);
                var compressedFileName = $"{fileId}_compressed{extension}";
                var compressedFilePath = Path.Combine(compressedFolder, compressedFileName);

                _logger.LogInformation($"开始压缩视频: {originalFilePath} -> {compressedFilePath}");

                // 根据压缩质量计算CRF值 (质量1-10 对应 CRF 35-18)
                var crf = 53 - (compressionQuality * 3.5);
                crf = Math.Max(18, Math.Min(35, crf)); // 限制在18-35范围内

                await FFMpegArguments
                    .FromFileInput(originalFilePath)
                    .OutputToFile(compressedFilePath, true, options => options
                        .WithVideoCodec(VideoCodec.LibX264)
                        .WithConstantRateFactor((int)crf)
                        .WithVariableBitrate(4)
                        .WithVideoFilters(filterOptions => filterOptions
                            .Scale(VideoSize.Hd)) // 限制最大分辨率为720p
                        .WithFastStart())
                    .NotifyOnProgress(timeSpan =>
                    {
                        // 计算真实的压缩进度
                        if (totalDuration.TotalSeconds > 0)
                        {
                            var progressPercent = Math.Min(95, (int)((timeSpan.TotalSeconds / totalDuration.TotalSeconds) * 100));
                            progress.Progress = progressPercent;
                            _logger.LogDebug($"压缩进度: {progressPercent}% (已处理: {timeSpan.TotalSeconds:F1}s / 总时长: {totalDuration.TotalSeconds:F1}s)");
                        }
                    })
                    .ProcessAsynchronously();

                // 获取压缩后文件大小
                var compressedFileInfo = new FileInfo(compressedFilePath);
                progress.CompressedSize = compressedFileInfo.Length;
                progress.CompressedUrl = $"/videos/compressed/{dateFolder}/{compressedFileName}";
                progress.Progress = 100;
                progress.Status = "completed";
                progress.EndTime = DateTime.Now;

                _logger.LogInformation($"视频压缩完成: {compressedFilePath}, 原始大小: {progress.OriginalSize}, 压缩后大小: {progress.CompressedSize}");

                // 更新数据库中的视频URL为压缩后的URL，并标记为压缩完成，状态改为上架
                await UpdateVideoAfterCompression(originalFilePath, progress.CompressedUrl, fileId);

                // 删除原始文件以节省空间
                await DeleteOriginalFile(originalFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"视频压缩失败: {originalFilePath}");
                var progress = _compressionProgress[fileId];
                progress.Status = "failed";
                progress.ErrorMessage = ex.Message;
                progress.EndTime = DateTime.Now;

                // 更新数据库中的视频状态为失败
                await UpdateVideoStatus(fileId, 2); // 2=视频失败

                // 删除原始文件（压缩失败也要删除）
                await DeleteOriginalFile(originalFilePath);
            }
        }

        /// <summary>
        /// 获取压缩进度
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>压缩进度信息</returns>
        public VideoCompressionProgress? GetCompressionProgress(string fileId)
        {
            _compressionProgress.TryGetValue(fileId, out var progress);
            return progress;
        }

        /// <summary>
        /// 清理过期的压缩进度记录
        /// </summary>
        public void CleanupExpiredProgress()
        {
            var expiredKeys = _compressionProgress
                .Where(kvp => kvp.Value.EndTime.HasValue &&
                             DateTime.Now - kvp.Value.EndTime.Value > TimeSpan.FromHours(24))
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _compressionProgress.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// 压缩完成后更新视频信息
        /// </summary>
        /// <param name="originalFilePath">原始文件路径</param>
        /// <param name="compressedUrl">压缩后的URL</param>
        /// <param name="fileId">文件ID</param>
        private async Task UpdateVideoAfterCompression(string originalFilePath, string compressedUrl, string fileId)
        {
            try
            {
                _logger.LogInformation("开始更新视频URL: {OriginalPath} -> {CompressedUrl}", originalFilePath, compressedUrl);

                // 创建新的作用域来避免生命周期问题
                using var scope = _serviceProvider.CreateScope();
                var videoService = scope.ServiceProvider.GetRequiredService<VideoService>();
                var videoDAL = scope.ServiceProvider.GetRequiredService<DAL.VideoDAL.VideoDAL>();

                // 通过FileId查找视频记录
                var queryable = new DAL.VideoDAL.VideoDAL.Queryable
                {
                    PageIndex = 1,
                    PageSize = 1000
                };
                var result = await videoDAL.GetPagedListAsync(queryable);
                var targetVideo = result.Items?.FirstOrDefault(v => v.FileId == fileId);

                if (targetVideo != null)
                {
                    // 解析Questions字段
                    List<VideoQuestionDto>? questions = null;
                    if (!string.IsNullOrEmpty(targetVideo.Questions))
                    {
                        try
                        {
                            questions = System.Text.Json.JsonSerializer.Deserialize<List<VideoQuestionDto>>(targetVideo.Questions);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"解析视频问题失败: VideoId={targetVideo.Id}");
                        }
                    }

                    // 创建更新DTO
                    var updateDto = new VideoUpdateDto
                    {
                        Id = targetVideo.Id,
                        Title = targetVideo.Title,
                        Description = targetVideo.Description,
                        VideoUrl = compressedUrl, // 更新为压缩后的URL
                        CoverUrl = targetVideo.CoverUrl,
                        Duration = targetVideo.Duration,
                        RewardAmount = targetVideo.RewardAmount,
                        Questions = questions,
                        Status = 1, // 压缩完成后状态改为上架
                        FileId = targetVideo.FileId
                    };

                    // 创建系统用户信息用于更新
                    var systemUserInfo = new CurrentUserInfoDto
                    {
                        UserId = "SYSTEM",
                        UserName = "系统自动压缩",
                        UserType = 1 // 超级管理员权限
                    };

                    await videoService.UpdateVideoAsync(updateDto, systemUserInfo);
                    _logger.LogInformation($"成功更新视频URL: VideoId={targetVideo.Id}, NewUrl={compressedUrl}");
                }
                else
                {
                    _logger.LogWarning($"未找到匹配的视频记录: FileId={fileId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新视频URL失败: {originalFilePath} -> {compressedUrl}");
            }
        }

        /// <summary>
        /// 删除原始文件
        /// </summary>
        /// <param name="originalFilePath">原始文件路径</param>
        private async Task DeleteOriginalFile(string originalFilePath)
        {
            try
            {
                if (File.Exists(originalFilePath))
                {
                    await Task.Run(() => File.Delete(originalFilePath));
                    _logger.LogInformation($"已删除原始文件: {originalFilePath}");
                }
            }
            catch (Exception deleteEx)
            {
                _logger.LogError(deleteEx, $"删除原始文件失败: {originalFilePath}");
            }
        }

        /// <summary>
        /// 更新视频状态
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="videoStatus">视频状态</param>
        private async Task UpdateVideoStatus(string fileId, byte videoStatus)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var videoService = scope.ServiceProvider.GetRequiredService<VideoService>();
                var videoDAL = scope.ServiceProvider.GetRequiredService<DAL.VideoDAL.VideoDAL>();

                // 通过FileId查找视频记录
                var queryable = new DAL.VideoDAL.VideoDAL.Queryable
                {
                    PageIndex = 1,
                    PageSize = 1000
                };
                var result = await videoDAL.GetPagedListAsync(queryable);
                var targetVideo = result.Items?.FirstOrDefault(v => v.FileId == fileId);

                if (targetVideo != null)
                {
                    // 解析Questions字段
                    List<VideoQuestionDto>? questions = null;
                    if (!string.IsNullOrEmpty(targetVideo.Questions))
                    {
                        try
                        {
                            questions = System.Text.Json.JsonSerializer.Deserialize<List<VideoQuestionDto>>(targetVideo.Questions);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"解析视频问题失败: VideoId={targetVideo.Id}");
                        }
                    }

                    // 创建更新DTO
                    var updateDto = new VideoUpdateDto
                    {
                        Id = targetVideo.Id,
                        Title = targetVideo.Title,
                        Description = targetVideo.Description,
                        VideoUrl = targetVideo.VideoUrl,
                        CoverUrl = targetVideo.CoverUrl,
                        Duration = targetVideo.Duration,
                        RewardAmount = targetVideo.RewardAmount,
                        Questions = questions,
                        Status = videoStatus,
                        FileId = targetVideo.FileId
                    };

                    // 创建系统用户信息
                    var systemUserInfo = new CurrentUserInfoDto
                    {
                        UserId = "SYSTEM",
                        UserName = "系统压缩服务",
                        UserType = 1
                    };

                    await videoService.UpdateVideoAsync(updateDto, systemUserInfo);
                    _logger.LogInformation($"已更新视频状态: VideoId={targetVideo.Id}, Status={videoStatus}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新视频状态失败: FileId={fileId}, Status={videoStatus}");
            }
        }

    }
}
