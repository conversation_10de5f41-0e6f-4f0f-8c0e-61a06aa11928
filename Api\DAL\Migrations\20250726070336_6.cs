﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DAL.Migrations
{
    /// <inheritdoc />
    public partial class _6 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CompressionStatus",
                table: "videos");

            migrationBuilder.AlterColumn<byte>(
                name: "Status",
                table: "videos",
                type: "tinyint unsigned",
                nullable: false,
                comment: "状态:0下架,1上架,2失败,3压缩中",
                oldClrType: typeof(byte),
                oldType: "tinyint unsigned",
                oldComment: "状态:0下架,1上架");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<byte>(
                name: "Status",
                table: "videos",
                type: "tinyint unsigned",
                nullable: false,
                comment: "状态:0下架,1上架",
                oldClrType: typeof(byte),
                oldType: "tinyint unsigned",
                oldComment: "状态:0下架,1上架,2失败,3压缩中");

            migrationBuilder.AddColumn<byte>(
                name: "CompressionStatus",
                table: "videos",
                type: "tinyint unsigned",
                nullable: false,
                defaultValue: (byte)0,
                comment: "压缩状态:0未压缩,1压缩中,2压缩完成,3压缩失败");
        }
    }
}
