﻿using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DAL.Migrations
{
    /// <inheritdoc />
    public partial class _1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "id",
                table: "users",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "user_audits",
                newName: "Id");

            migrationBuilder.AlterColumn<string>(
                name: "UserId",
                table: "wechat_payments",
                type: "longtext",
                nullable: false,
                comment: "用户ID",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "用户ID")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Id",
                table: "users",
                type: "varchar(32)",
                maxLength: 32,
                nullable: false,
                comment: "主键ID (32位GUID字符串)",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "ID")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);

            migrationBuilder.AlterColumn<string>(
                name: "UserId",
                table: "user_transfers",
                type: "longtext",
                nullable: false,
                comment: "被转移用户ID",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "被转移用户ID")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "UserId",
                table: "user_batch_records",
                type: "varchar(50)",
                maxLength: 50,
                nullable: false,
                comment: "用户ID",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "用户ID")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "user_id",
                table: "user_audits",
                type: "longtext",
                nullable: true,
                comment: "被审核用户ID",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "被审核用户ID")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Id",
                table: "user_audits",
                type: "varchar(32)",
                maxLength: 32,
                nullable: false,
                comment: "主键ID (32位GUID字符串)",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "ID")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Id",
                table: "users",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "user_audits",
                newName: "id");

            migrationBuilder.AlterColumn<int>(
                name: "UserId",
                table: "wechat_payments",
                type: "int",
                nullable: false,
                comment: "用户ID",
                oldClrType: typeof(string),
                oldType: "longtext",
                oldComment: "用户ID")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "id",
                table: "users",
                type: "int",
                nullable: false,
                comment: "ID",
                oldClrType: typeof(string),
                oldType: "varchar(32)",
                oldMaxLength: 32,
                oldComment: "主键ID (32位GUID字符串)")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "UserId",
                table: "user_transfers",
                type: "int",
                nullable: false,
                comment: "被转移用户ID",
                oldClrType: typeof(string),
                oldType: "longtext",
                oldComment: "被转移用户ID")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "UserId",
                table: "user_batch_records",
                type: "int",
                nullable: false,
                comment: "用户ID",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldMaxLength: 50,
                oldComment: "用户ID")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "user_id",
                table: "user_audits",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "被审核用户ID",
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true,
                oldComment: "被审核用户ID")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<int>(
                name: "id",
                table: "user_audits",
                type: "int",
                nullable: false,
                comment: "ID",
                oldClrType: typeof(string),
                oldType: "varchar(32)",
                oldMaxLength: 32,
                oldComment: "主键ID (32位GUID字符串)")
                .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn)
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }
    }
}
