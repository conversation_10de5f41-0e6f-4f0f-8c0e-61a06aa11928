using DAL.VideoDAL;
using Entity.Dto.VideoDto;
using Common.Autofac;

namespace BLL.VideoService
{
    /// <summary>
    /// 标签统计服务（简化版）
    /// 暂时只返回"未指定标签"统计，满足当前仪表板需求
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class TagStatisticsService(UserDAL userDAL)
    {
        private readonly UserDAL _userDAL = userDAL;

        /// <summary>
        /// 获取标签统计信息
        /// 当前简化实现：所有用户都归类为"未指定标签"
        /// </summary>
        /// <returns>标签统计列表</returns>
        public async Task<List<TagStatisticsDto>> GetTagStatisticsAsync()
        {
            // 获取总用户数
            var totalUsers = await _userDAL.GetCountAsync();

            // 返回简化的标签统计：所有用户都是"未指定标签"
            return
            [
                new TagStatisticsDto
                {
                    TagId = null,
                    TagName = "未指定标签",
                    UserCount = totalUsers,
                    IsUntagged = true,
                    Percentage = 100.0m
                }
            ];
        }

        /// <summary>
        /// 获取未指定标签的用户数量
        /// </summary>
        /// <returns>未指定标签的用户数量</returns>
        public async Task<int> GetUntaggedUserCountAsync()
        {
            // 当前所有用户都是未指定标签
            return await _userDAL.GetCountAsync();
        }
    }
}
