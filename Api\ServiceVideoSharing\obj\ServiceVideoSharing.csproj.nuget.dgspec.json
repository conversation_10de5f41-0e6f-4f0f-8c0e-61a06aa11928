{"format": 1, "restore": {"D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\ServiceVideoSharing\\ServiceVideoSharing.csproj": {}}, "projects": {"D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\BLL\\BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\BLL\\BLL.csproj", "projectName": "BLL", "projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\BLL\\BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\BLL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj"}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\DAL.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\DAL.csproj"}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\Entity.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "FFMpegCore": {"target": "Package", "version": "[5.0.2, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.2, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj", "projectName": "Common", "projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AlibabaCloud.OpenApiClient": {"target": "Package", "version": "[0.1.13, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[6.8.1, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.7.17, )"}, "log4net": {"target": "Package", "version": "[2.0.15, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\DAL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\DAL.csproj", "projectName": "DAL", "projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\DAL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj"}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\Entity.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\Entity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\Entity.csproj", "projectName": "Entity", "projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\Entity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Entity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\ServiceVideoSharing\\ServiceVideoSharing.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\ServiceVideoSharing\\ServiceVideoSharing.csproj", "projectName": "ServiceVideoSharing", "projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\ServiceVideoSharing\\ServiceVideoSharing.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\ServiceVideoSharing\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\BLL\\BLL.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\BLL\\BLL.csproj"}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\Common\\Common.csproj"}, "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\DAL.csproj": {"projectPath": "D:\\MyWork\\works\\hangzhou-service-video-sharing\\Api\\DAL\\DAL.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0, )"}, "FFMpegCore": {"target": "Package", "version": "[5.0.2, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.5, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}