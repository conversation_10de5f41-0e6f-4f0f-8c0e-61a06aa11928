using BLL.VideoService;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 仪表板控制器
    /// 提供类似截图的综合统计仪表板功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class DashboardController(DashboardService dashboardService) : BaseController
    {
        private readonly DashboardService _dashboardService = dashboardService;

        /// <summary>
        /// 获取综合仪表板数据（主接口）
        /// 返回类似截图的完整仪表板数据，包括：
        /// - 数据汇总（会员总数、今日新增会员、订单总数）
        /// - 标签统计（未指定标签用户数）
        /// - 课程统计（观看人数、完播人数、完播率）
        /// - 答题统计（答题人数、正确人数、正确率）
        /// - 红包统计（答题红包数、答题红包金额）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>综合仪表板数据</returns>
        [HttpGet]
        public async Task<Result<DashboardDto>> GetDashboard(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var dashboard = await _dashboardService.GetDashboardDataAsync(startDate, endDate);
            return Success(dashboard, "仪表板数据获取成功");
        }

        /// <summary>
        /// 获取数据汇总
        /// 包括会员总数、今日新增会员、订单总数等关键指标
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>数据汇总</returns>
        [HttpGet("summary")]
        public async Task<Result<DashboardSummaryDto>> GetSummary(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today;
            var end = endDate ?? DateTime.Today.AddDays(1).AddSeconds(-1);
            var summary = await _dashboardService.GetDashboardSummaryAsync(start, end);
            return Success(summary, "数据汇总获取成功");
        }

        /// <summary>
        /// 获取标签统计
        /// 包括各标签的用户数量和未指定标签的用户数量
        /// </summary>
        /// <returns>标签统计列表</returns>
        [HttpGet("tags")]
        public async Task<Result<List<TagStatisticsDto>>> GetTagStatistics()
        {
            var tagStats = await _dashboardService.GetTagStatisticsAsync();
            return Success(tagStats, "标签统计获取成功");
        }

        /// <summary>
        /// 获取课程统计
        /// 包括观看人数、完播人数、完播率等
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>课程统计</returns>
        [HttpGet("course")]
        public async Task<Result<CourseStatisticsDto>> GetCourseStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today;
            var end = endDate ?? DateTime.Today.AddDays(1).AddSeconds(-1);
            var courseStats = await _dashboardService.GetCourseStatisticsAsync(start, end);
            return Success(courseStats, "课程统计获取成功");
        }

        /// <summary>
        /// 获取答题统计
        /// 包括答题人数、正确人数、正确率等
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>答题统计</returns>
        [HttpGet("answer")]
        public async Task<Result<AnswerStatisticsDto>> GetAnswerStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today;
            var end = endDate ?? DateTime.Today.AddDays(1).AddSeconds(-1);
            var answerStats = await _dashboardService.GetAnswerStatisticsAsync(start, end);
            return Success(answerStats, "答题统计获取成功");
        }

        /// <summary>
        /// 获取红包统计
        /// 包括红包数量、红包金额等
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>红包统计</returns>
        [HttpGet("reward")]
        public async Task<Result<RewardStatisticsDto>> GetRewardStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today;
            var end = endDate ?? DateTime.Today.AddDays(1).AddSeconds(-1);
            var rewardStats = await _dashboardService.GetRewardStatisticsAsync(start, end);
            return Success(rewardStats, "红包统计获取成功");
        }

        /// <summary>
        /// 获取订单统计
        /// 包括订单数量、订单金额等
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>订单统计</returns>
        [HttpGet("order")]
        public async Task<Result<OrderStatisticsDto>> GetOrderStatistics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today;
            var end = endDate ?? DateTime.Today.AddDays(1).AddSeconds(-1);
            var orderStats = await _dashboardService.GetOrderStatisticsAsync(start, end);
            return Success(orderStats, "订单统计获取成功");
        }

        /// <summary>
        /// 获取今日数据快照
        /// 快速获取今日的所有关键数据
        /// </summary>
        /// <returns>今日数据快照</returns>
        [HttpGet("today")]
        public async Task<Result<DashboardDto>> GetTodaySnapshot()
        {
            var snapshot = await _dashboardService.GetTodaySnapshotAsync();
            return Success(snapshot, "今日数据快照获取成功");
        }

        /// <summary>
        /// 获取本月数据快照
        /// 快速获取本月的所有关键数据
        /// </summary>
        /// <returns>本月数据快照</returns>
        [HttpGet("thisMonth")]
        public async Task<Result<DashboardDto>> GetThisMonthSnapshot()
        {
            var snapshot = await _dashboardService.GetThisMonthSnapshotAsync();
            return Success(snapshot, "本月数据快照获取成功");
        }

        /// <summary>
        /// 获取关键指标摘要
        /// 提供最核心的几个指标数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>关键指标摘要</returns>
        [HttpGet("metrics")]
        public async Task<Result<KeyMetricsSummaryDto>> GetKeyMetrics(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var metrics = await _dashboardService.GetKeyMetricsSummaryAsync(startDate, endDate);
            return Success(metrics, "关键指标摘要获取成功");
        }

        /// <summary>
        /// 获取数据对比（今日vs昨日）
        /// 提供今日和昨日的数据对比
        /// </summary>
        /// <returns>数据对比</returns>
        [HttpGet("compare/today-yesterday")]
        public async Task<Result<object>> GetTodayVsYesterday()
        {
            var (today, yesterday) = await _dashboardService.GetTodayVsYesterdayAsync();
            return Success((object)new { Today = today, Yesterday = yesterday }, "数据对比获取成功");
        }
    }
}
