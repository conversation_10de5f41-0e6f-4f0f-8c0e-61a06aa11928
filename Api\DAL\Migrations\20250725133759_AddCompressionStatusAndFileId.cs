﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DAL.Migrations
{
    /// <inheritdoc />
    public partial class AddCompressionStatusAndFileId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<byte>(
                name: "CompressionStatus",
                table: "videos",
                type: "tinyint unsigned",
                nullable: false,
                defaultValue: (byte)0,
                comment: "压缩状态:0未压缩,1压缩中,2压缩完成,3压缩失败");

            migrationBuilder.AddColumn<string>(
                name: "FileId",
                table: "videos",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                comment: "文件ID，用于关联压缩进度")
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CompressionStatus",
                table: "videos");

            migrationBuilder.DropColumn(
                name: "FileId",
                table: "videos");
        }
    }
}
