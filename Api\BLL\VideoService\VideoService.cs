using BLL.SysService;
using Common.Autofac;
using Common.Caches;
using Common.Exceptions;
using DAL.VideoDAL;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Entity.Extensions;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace BLL.VideoService
{
    /// <summary>
    /// 视频业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class VideoService(VideoDAL videoDAL, UserDAL userDAL, SysUserDAL sysUserDAL, SysLogService logService)
    {
        private readonly VideoDAL _videoDAL = videoDAL;
        private readonly UserDAL _userDAL = userDAL;
        private readonly SysUserDAL _sysUserDAL = sysUserDAL;
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 创建视频
        /// </summary>
        /// <param name="createDto">创建视频DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>视频ID</returns>
        public async Task<int> CreateVideoAsync(VideoCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 创建视频实体
            var video = new Video
            {
                Title = createDto.Title,
                Description = createDto.Description,
                VideoUrl = createDto.VideoUrl,
                CoverUrl = createDto.CoverUrl,
                Duration = createDto.Duration,
                RewardAmount = createDto.RewardAmount,
                Questions = createDto.Questions != null ? System.Text.Json.JsonSerializer.Serialize(createDto.Questions) : null,
                Status = createDto.FileId != null ? (byte)3 : (byte)1, // 如果有FileId说明启用了压缩，状态为3(压缩中)，否则为1(上架)
                FileId = createDto.FileId
            };

            // 使用扩展方法自动填充创建人信息和创建时间
            video.InitializeForAdd(currentUserInfo);

            // 添加视频
            await _videoDAL.AddAsync(video);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "视频管理",
                Operation = "创建视频",
                BusinessObject = "Video",
                ObjectId = video.Id.ToString(),
                DetailedInfo = $"创建视频：{video.Title}",
                AfterData = video,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return video.Id;
        }

        /// <summary>
        /// 更新视频信息
        /// </summary>
        /// <param name="updateDto">更新视频DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateVideoAsync(VideoUpdateDto updateDto, CurrentUserInfoDto currentUserInfo)
        {
            // 获取原视频信息
            var video = await _videoDAL.GetByIdAsync(updateDto.Id)
                ?? throw new BusinessException("视频不存在");

            // 权限验证：只有超级管理员、管理员或视频创建者可以更新视频
            if (currentUserInfo.UserType != 1 && currentUserInfo.UserType != 2 && video.CreatedBy != currentUserInfo.UserId)
            {
                throw new BusinessException("您无权限更新此视频");
            }

            var beforeData = new { video.Title, video.Description, video.VideoUrl, video.CoverUrl, video.Duration, video.RewardAmount, video.Questions };

            // 更新视频信息
            video.Title = updateDto.Title;
            video.Description = updateDto.Description;
            video.VideoUrl = updateDto.VideoUrl;
            video.CoverUrl = updateDto.CoverUrl;
            video.Duration = updateDto.Duration;
            video.RewardAmount = updateDto.RewardAmount;
            video.Questions = updateDto.Questions != null ? JsonSerializer.Serialize(updateDto.Questions, MemoryCacheHelper.DefaultJsonOptions) : null;
            video.FileId = updateDto.FileId;
            
            // 如果指定了状态，则更新状态
            if (updateDto.Status.HasValue)
            {
                video.Status = updateDto.Status.Value;
            }

            var result = await _videoDAL.UpdateAsync(video);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "视频管理",
                Operation = "更新视频",
                BusinessObject = "Video",
                ObjectId = video.Id.ToString(),
                DetailedInfo = $"更新视频：{video.Title}",
                BeforeData = beforeData,
                AfterData = new { video.Title, video.Description, video.VideoUrl, video.CoverUrl, video.Duration, video.RewardAmount, video.Questions },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 更新视频状态
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <param name="status">状态:0下架,1上架,2失败,3压缩中</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateVideoStatusAsync(int videoId, byte status, CurrentUserInfoDto currentUserInfo)
        {
            var video = await _videoDAL.GetByIdAsync(videoId)
                ?? throw new BusinessException("视频不存在");

            var beforeStatus = video.Status;
            var result = await _videoDAL.UpdateStatusAsync(videoId, status);

            // 记录业务日志
            var statusText = status switch
            {
                0 => "下架",
                1 => "上架",
                2 => "失败",
                3 => "压缩中",
                _ => "未知状态"
            };

            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "视频管理",
                Operation = "更新视频状态",
                BusinessObject = "Video",
                ObjectId = videoId.ToString(),
                DetailedInfo = $"更新视频状态：{video.Title}，状态：{statusText}",
                BeforeData = new { Status = beforeStatus },
                AfterData = new { Status = status },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 删除视频
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteVideoAsync(int videoId, CurrentUserInfoDto currentUserInfo)
        {
            // 获取视频信息
            var video = await _videoDAL.GetByIdAsync(videoId)
                ?? throw new BusinessException("视频不存在");

            // 权限验证：只有超级管理员、管理员或视频创建者可以删除视频
            if (currentUserInfo.UserType != 1 && currentUserInfo.UserType != 2 && video.CreatedBy != currentUserInfo.UserId)
            {
                throw new BusinessException("您无权限删除此视频");
            }

            var result = await _videoDAL.DeleteAsync(video);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "视频管理",
                Operation = "删除视频",
                BusinessObject = "Video",
                ObjectId = videoId.ToString(),
                DetailedInfo = $"删除视频：{video.Title}",
                BeforeData = video,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 获取视频详情
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <returns>视频响应DTO</returns>
        public async Task<VideoResponseDto?> GetVideoAsync(int videoId)
        {
            var video = await _videoDAL.GetByIdAsync(videoId);
            if (video == null) return null;

            // 获取创建者信息
            var creator = await _sysUserDAL.GetByIdAsync(video.CreatedBy ?? "");

            return new VideoResponseDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                VideoUrl = video.VideoUrl,
                CoverUrl = video.CoverUrl,
                Duration = video.Duration,
                RewardAmount = video.RewardAmount,
                Questions = !string.IsNullOrEmpty(video.Questions) ? JsonSerializer.Deserialize<List<VideoQuestionDto>>(video.Questions, MemoryCacheHelper.DefaultJsonOptions) : null,
                CreatedBy = video.CreatedBy,
                CreatorName = video.CreatorName ?? creator?.RealName ?? "未知用户",
                Status = video.Status,
                ViewCount = video.ViewCount,
                CreateTime = video.CreateTime
            };
        }

        /// <summary>
        /// 分页查询视频列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<VideoResponseDto>> GetVideoPagedListAsync(VideoQueryDto queryDto)
        {
            var queryable = new VideoDAL.Queryable
            {
                Title = queryDto.Title,
                CreatedBy = queryDto.CreatedBy,
                Status = queryDto.Status,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            var result = await _videoDAL.GetPagedListAsync(queryable);

            var responseList = (result.Items ?? []).Select(video => new VideoResponseDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                VideoUrl = video.VideoUrl,
                CoverUrl = video.CoverUrl,
                Duration = video.Duration,
                RewardAmount = video.RewardAmount,
                Questions = !string.IsNullOrEmpty(video.Questions) ? JsonSerializer.Deserialize<List<VideoQuestionDto>>(video.Questions, MemoryCacheHelper.DefaultJsonOptions) : null,
                CreatedBy = video.CreatedBy,
                CreatorName = video.CreatorName,
                Status = video.Status,
                ViewCount = video.ViewCount,
                CreateTime = video.CreateTime
            }).ToList();

            return new PagedResult<VideoResponseDto>
            {
                Items = responseList,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }

        /// <summary>
        /// 获取创建者的视频列表
        /// </summary>
        /// <param name="createdBy">创建者ID</param>
        /// <param name="status">状态筛选</param>
        /// <returns>视频列表</returns>
        public async Task<List<VideoResponseDto>> GetVideosByCreatorAsync(string createdBy, byte? status = null)
        {
            var videos = await _videoDAL.GetByCreatorIdAsync(createdBy);

            // 应用状态过滤
            if (status.HasValue)
            {
                videos = [.. videos.Where(v => v.Status == status.Value)];
            }

            return [.. videos.Select(video => new VideoResponseDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                VideoUrl = video.VideoUrl,
                CoverUrl = video.CoverUrl,
                Duration = video.Duration,
                RewardAmount = video.RewardAmount,
                Questions = !string.IsNullOrEmpty(video.Questions) ? JsonSerializer.Deserialize<List<VideoQuestionDto>?>(video.Questions, MemoryCacheHelper.DefaultJsonOptions) : null,
                CreatedBy = video.CreatedBy,
                CreatorName = video.CreatorName,
                Status = video.Status,
                ViewCount = video.ViewCount,
                CreateTime = video.CreateTime
            })];
        }

        /// <summary>
        /// 获取单个视频统计信息
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <returns>视频统计</returns>
        public async Task<VideoStatisticsDto> GetVideoStatisticsAsync(int videoId)
        {
            // 获取视频基本信息
            var video = await _videoDAL.GetByIdAsync(videoId) ?? throw new BusinessException("视频不存在");

            // 获取视频统计数据
            var videoStats = await _videoDAL.GetVideoStatisticsAsync(videoId);

            return new VideoStatisticsDto
            {
                TotalViewCount = videoStats.ViewCount,
                CompleteViewCount = videoStats.CompleteViewCount,
                CompleteRate = videoStats.CompleteRate,
                TotalCount = 1,
                PublishedCount = video.Status == 1 ? 1 : 0,
                PendingCount = video.Status == 0 ? 1 : 0,
                OfflineCount = video.Status == 2 ? 1 : 0,
                TotalDuration = video.Duration
            };
        }

        /// <summary>
        /// 获取整体视频统计信息
        /// </summary>
        /// <param name="createdBy">创建者ID（可选）</param>
        /// <returns>视频统计</returns>
        public async Task<VideoStatisticsDto> GetOverallVideoStatisticsAsync(string? createdBy = null)
        {
            // 使用查询条件获取统计数据
            var allVideosQuery = new VideoDAL.Queryable();
            if (!string.IsNullOrEmpty(createdBy))
            {
                allVideosQuery.CreatedBy = createdBy;
            }
            var allVideos = await _videoDAL.GetPagedListAsync(allVideosQuery);

            var statistics = new
            {
                allVideos.TotalCount,
                PublishedCount = allVideos.List?.Count(v => v.Status == 1) ?? 0,
                PendingCount = allVideos.List?.Count(v => v.Status == 0) ?? 0,
                OfflineCount = allVideos.List?.Count(v => v.Status != 0 && v.Status != 1) ?? 0,
                TotalViewCount = (long)(allVideos.List?.Sum(v => v.ViewCount) ?? 0),
                TotalDuration = allVideos.List?.Sum(v => v.Duration) ?? 0
            };

            return new VideoStatisticsDto
            {
                TotalCount = statistics.TotalCount,
                PublishedCount = statistics.PublishedCount,
                PendingCount = statistics.PendingCount,
                OfflineCount = statistics.OfflineCount,
                TotalViewCount = (int)statistics.TotalViewCount,
                TotalDuration = statistics.TotalDuration
            };
        }
    }
}
